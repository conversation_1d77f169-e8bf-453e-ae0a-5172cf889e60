{"transactions": [{"hash": "0xa77740250aab38cb7180ac1dd59b201cef97f6975f6f7230ca8dd8f88ea4a5f9", "transactionType": "CREATE2", "contractName": "StateView", "contractAddress": "0x571291b572ed32ce6751a2cb2486ebee8defb9b4", "function": null, "arguments": ["0x05E73354cFDd6745C338b50BcFDfA3Aa6fA03408"], "transaction": {"from": "0x7024cc7e60d6560f0b5877da2bb921fcbf1f4375", "to": "0x4e59b44847b379578588920ca78fbf26c0b4956c", "gas": "0x1129a6", "value": "0x0", "input": "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", "nonce": "0x43", "chainId": "0x14a34"}, "additionalContracts": [], "isFixedGasLimit": false}], "receipts": [{"status": "0x1", "cumulativeGasUsed": "0x1a0952", "logs": [], "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "type": "0x2", "transactionHash": "0xa77740250aab38cb7180ac1dd59b201cef97f6975f6f7230ca8dd8f88ea4a5f9", "transactionIndex": "0x6", "blockHash": "0x09903ea7bc75dd70fb6f82d78406109a86e7b365a2c829cab8af99d61a487eee", "blockNumber": "0x123467f", "gasUsed": "0xc6ced", "effectiveGasPrice": "0xe4396", "from": "0x7024cc7e60d6560f0b5877da2bb921fcbf1f4375", "to": "0x4e59b44847b379578588920ca78fbf26c0b4956c", "contractAddress": "0x571291b572ed32ce6751a2cb2486ebee8defb9b4", "l1BaseFeeScalar": "0x44d", "l1BlobBaseFee": "0x9e", "l1BlobBaseFeeScalar": "0xa118b", "l1Fee": "0x3854304cb1", "l1GasPrice": "0x21385ef98", "l1GasUsed": "0x6041"}], "libraries": [], "pending": [], "returns": {"state": {"internal_type": "contract IStateView", "value": "0x571291b572ed32ce6751a2Cb2486EbEe8DEfB9B4"}}, "timestamp": 1733946351, "chain": 84532, "commit": "645fbc2e"}