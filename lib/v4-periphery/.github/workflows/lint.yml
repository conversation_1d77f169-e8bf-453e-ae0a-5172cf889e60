name: Lint

on:
  push:
    branches:
      - main
  pull_request:

jobs:
  run-linters:
    name: <PERSON><PERSON>
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3
        with:
          submodules: recursive

      - name: Install Foundry
        uses: foundry-rs/foundry-toolchain@v1
        with:
          version: stable

      - name: Check format
        run: forge fmt --check
