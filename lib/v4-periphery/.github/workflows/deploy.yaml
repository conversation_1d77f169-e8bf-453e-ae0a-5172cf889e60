name: Release
on:
  # manual trigger
  workflow_dispatch:

jobs:
  deploy:
    name: release
    runs-on:
      group: npm-deploy
    environment:
      name: release
    permissions:
      id-token: write
      contents: write
    steps:
      - name: Load secret
        uses: 1password/load-secrets-action@581a835fb51b8e7ec56b71cf2ffddd7e68bb25e0
        with:
          # Export loaded secrets as environment variables
          export-env: true
        env:
          OP_SERVICE_ACCOUNT_TOKEN: ${{ secrets.OP_SERVICE_ACCOUNT_TOKEN }}
          # You may need to change this to your vault name and secret name
          # Refer to it by calling env.NPM_TOKEN
          # This token is also limited by IP to ONLY work on the runner
          NPM_TOKEN: op://npm-deploy/npm-runner-token/secret

      - name: Checkout
        uses: actions/checkout@f43a0e5ff2bd294095638e18286ca9a3d1956744

      - name: Setup Node
        uses: actions/setup-node@v4.2.0
        with:
          node-version: "20.x"
          registry-url: "https://registry.npmjs.org"
          scope: "@uniswap"

      - name: Install Foundry
        uses: foundry-rs/foundry-toolchain@v1
        with:
          version: stable

      - name: Install dependencies
        run: |
          git submodule update --init --recursive

      - name: Compile
        run: forge build

      - name: Release
        env:
          NODE_AUTH_TOKEN: ${{ env.NPM_TOKEN }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          npm set "//registry.npmjs.org/:_authToken" ${{ env.NPM_TOKEN }} 
          npm publish --provenance --access public
