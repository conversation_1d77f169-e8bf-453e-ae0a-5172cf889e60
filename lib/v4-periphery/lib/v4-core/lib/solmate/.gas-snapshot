AuthTest:testCallFunctionAsOwner() (gas: 29784)
AuthTest:testCallFunctionWithPermissiveAuthority() (gas: 124292)
AuthTest:testCallFunctionWithPermissiveAuthority(address) (runs: 256, μ: 129197, ~: 129235)
AuthTest:testFailCallFunctionAsNonOwner() (gas: 15523)
AuthTest:testFailCallFunctionAsNonOwner(address) (runs: 256, μ: 15685, ~: 15685)
AuthTest:testFailCallFunctionAsOwnerWithOutOfOrderAuthority() (gas: 136021)
AuthTest:testFailCallFunctionWithRestrictiveAuthority() (gas: 129144)
AuthTest:testFailCallFunctionWithRestrictiveAuthority(address) (runs: 256, μ: 129329, ~: 129329)
AuthTest:testFailSetAuthorityAsNonOwner() (gas: 18325)
AuthTest:testFailSetAuthorityAsNonOwner(address,address) (runs: 256, μ: 18594, ~: 18594)
AuthTest:testFailSetAuthorityWithRestrictiveAuthority() (gas: 129077)
AuthTest:testFailSetAuthorityWithRestrictiveAuthority(address,address) (runs: 256, μ: 129409, ~: 129409)
AuthTest:testFailTransferOwnershipAsNonOwner() (gas: 15641)
AuthTest:testFailTransferOwnershipAsNonOwner(address,address) (runs: 256, μ: 15922, ~: 15922)
AuthTest:testFailTransferOwnershipAsOwnerWithOutOfOrderAuthority() (gas: 136159)
AuthTest:testFailTransferOwnershipAsOwnerWithOutOfOrderAuthority(address) (runs: 256, μ: 136344, ~: 136344)
AuthTest:testFailTransferOwnershipWithRestrictiveAuthority() (gas: 129338)
AuthTest:testFailTransferOwnershipWithRestrictiveAuthority(address,address) (runs: 256, μ: 129588, ~: 129588)
AuthTest:testSetAuthorityAsOwner() (gas: 32214)
AuthTest:testSetAuthorityAsOwner(address) (runs: 256, μ: 32384, ~: 32384)
AuthTest:testSetAuthorityAsOwnerWithOutOfOrderAuthority() (gas: 226419)
AuthTest:testSetAuthorityWithPermissiveAuthority() (gas: 125962)
AuthTest:testSetAuthorityWithPermissiveAuthority(address,address) (runs: 256, μ: 130862, ~: 131055)
AuthTest:testTransferOwnershipAsOwner() (gas: 15298)
AuthTest:testTransferOwnershipAsOwner(address) (runs: 256, μ: 15469, ~: 15469)
AuthTest:testTransferOwnershipWithPermissiveAuthority() (gas: 127926)
AuthTest:testTransferOwnershipWithPermissiveAuthority(address,address) (runs: 256, μ: 131000, ~: 131000)
Bytes32AddressLibTest:testFillLast12Bytes() (gas: 223)
Bytes32AddressLibTest:testFromLast20Bytes() (gas: 191)
CREATE3Test:testDeployERC20() (gas: 853207)
CREATE3Test:testDeployERC20(bytes32,string,string,uint8) (runs: 256, μ: 899408, ~: 899427)
CREATE3Test:testFailDoubleDeployDifferentBytecode() (gas: 9079256848778914176)
CREATE3Test:testFailDoubleDeployDifferentBytecode(bytes32,bytes,bytes) (runs: 256, μ: 5830806919119382084, ~: 8937393460516727580)
CREATE3Test:testFailDoubleDeploySameBytecode() (gas: 9079256848778906219)
CREATE3Test:testFailDoubleDeploySameBytecode(bytes32,bytes) (runs: 256, μ: 5516047526412694750, ~: 8937393460516728714)
CREATE3Test:testPredictDeployERC20() (gas: 2102048)
DSTestPlusTest:testBound() (gas: 14214)
DSTestPlusTest:testBound(uint256,uint256,uint256) (runs: 256, μ: 2787, ~: 2793)
DSTestPlusTest:testBrutalizeMemory() (gas: 823)
DSTestPlusTest:testFailBoundMinBiggerThanMax() (gas: 309)
DSTestPlusTest:testFailBoundMinBiggerThanMax(uint256,uint256,uint256) (runs: 256, μ: 460, ~: 460)
DSTestPlusTest:testRelApproxEqBothZeroesPasses() (gas: 425)
ERC1155Test:testApproveAll() (gas: 31009)
ERC1155Test:testApproveAll(address,bool) (runs: 256, μ: 22771, ~: 31309)
ERC1155Test:testBatchBalanceOf() (gas: 157631)
ERC1155Test:testBatchBalanceOf(address[],uint256[],uint256[],bytes) (runs: 256, μ: 3564574, ~: 2943600)
ERC1155Test:testBatchBurn() (gas: 151074)
ERC1155Test:testBatchBurn(address,uint256[],uint256[],uint256[],bytes) (runs: 256, μ: 3627558, ~: 3049938)
ERC1155Test:testBatchMintToEOA() (gas: 137337)
ERC1155Test:testBatchMintToEOA(address,uint256[],uint256[],bytes) (runs: 256, μ: 3072824, ~: 2525648)
ERC1155Test:testBatchMintToERC1155Recipient() (gas: 995703)
ERC1155Test:testBatchMintToERC1155Recipient(uint256[],uint256[],bytes) (runs: 256, μ: 7395823, ~: 6396323)
ERC1155Test:testBurn() (gas: 38598)
ERC1155Test:testBurn(address,uint256,uint256,bytes,uint256) (runs: 256, μ: 39631, ~: 42098)
ERC1155Test:testFailBalanceOfBatchWithArrayMismatch() (gas: 7933)
ERC1155Test:testFailBalanceOfBatchWithArrayMismatch(address[],uint256[]) (runs: 256, μ: 58022, ~: 58788)
ERC1155Test:testFailBatchBurnInsufficientBalance() (gas: 136156)
ERC1155Test:testFailBatchBurnInsufficientBalance(address,uint256[],uint256[],uint256[],bytes) (runs: 256, μ: 1401354, ~: 590599)
ERC1155Test:testFailBatchBurnWithArrayLengthMismatch() (gas: 135542)
ERC1155Test:testFailBatchBurnWithArrayLengthMismatch(address,uint256[],uint256[],uint256[],bytes) (runs: 256, μ: 86139, ~: 76411)
ERC1155Test:testFailBatchMintToNonERC1155Recipient() (gas: 167292)
ERC1155Test:testFailBatchMintToNonERC1155Recipient(uint256[],uint256[],bytes) (runs: 256, μ: 3190100, ~: 2673077)
ERC1155Test:testFailBatchMintToRevertingERC1155Recipient() (gas: 358811)
ERC1155Test:testFailBatchMintToRevertingERC1155Recipient(uint256[],uint256[],bytes) (runs: 256, μ: 3381638, ~: 2864613)
ERC1155Test:testFailBatchMintToWrongReturnDataERC1155Recipient() (gas: 310743)
ERC1155Test:testFailBatchMintToWrongReturnDataERC1155Recipient(uint256[],uint256[],bytes) (runs: 256, μ: 3333596, ~: 2816572)
ERC1155Test:testFailBatchMintToZero() (gas: 131737)
ERC1155Test:testFailBatchMintToZero(uint256[],uint256[],bytes) (runs: 256, μ: 3130600, ~: 2612336)
ERC1155Test:testFailBatchMintWithArrayMismatch() (gas: 9600)
ERC1155Test:testFailBatchMintWithArrayMismatch(address,uint256[],uint256[],bytes) (runs: 256, μ: 66450, ~: 66511)
ERC1155Test:testFailBurnInsufficientBalance() (gas: 34852)
ERC1155Test:testFailBurnInsufficientBalance(address,uint256,uint256,uint256,bytes) (runs: 256, μ: 35106, ~: 38209)
ERC1155Test:testFailMintToNonERC155Recipient() (gas: 68191)
ERC1155Test:testFailMintToNonERC155Recipient(uint256,uint256,bytes) (runs: 256, μ: 68507, ~: 69197)
ERC1155Test:testFailMintToRevertingERC155Recipient() (gas: 259435)
ERC1155Test:testFailMintToRevertingERC155Recipient(uint256,uint256,bytes) (runs: 256, μ: 259682, ~: 260373)
ERC1155Test:testFailMintToWrongReturnDataERC155Recipient() (gas: 259389)
ERC1155Test:testFailMintToWrongReturnDataERC155Recipient(uint256,uint256,bytes) (runs: 256, μ: 259706, ~: 260397)
ERC1155Test:testFailMintToZero() (gas: 33705)
ERC1155Test:testFailMintToZero(uint256,uint256,bytes) (runs: 256, μ: 33815, ~: 34546)
ERC1155Test:testFailSafeBatchTransferFromToNonERC1155Recipient() (gas: 321377)
ERC1155Test:testFailSafeBatchTransferFromToNonERC1155Recipient(uint256[],uint256[],uint256[],bytes,bytes) (runs: 256, μ: 3541141, ~: 2963551)
ERC1155Test:testFailSafeBatchTransferFromToRevertingERC1155Recipient() (gas: 512956)
ERC1155Test:testFailSafeBatchTransferFromToRevertingERC1155Recipient(uint256[],uint256[],uint256[],bytes,bytes) (runs: 256, μ: 3732678, ~: 3155082)
ERC1155Test:testFailSafeBatchTransferFromToWrongReturnDataERC1155Recipient() (gas: 464847)
ERC1155Test:testFailSafeBatchTransferFromToWrongReturnDataERC1155Recipient(uint256[],uint256[],uint256[],bytes,bytes) (runs: 256, μ: 3684595, ~: 3107003)
ERC1155Test:testFailSafeBatchTransferFromToZero() (gas: 286556)
ERC1155Test:testFailSafeBatchTransferFromToZero(uint256[],uint256[],uint256[],bytes,bytes) (runs: 256, μ: 3505960, ~: 2928396)
ERC1155Test:testFailSafeBatchTransferFromWithArrayLengthMismatch() (gas: 162674)
ERC1155Test:testFailSafeBatchTransferFromWithArrayLengthMismatch(address,uint256[],uint256[],uint256[],bytes,bytes) (runs: 256, μ: 79144, ~: 76711)
ERC1155Test:testFailSafeBatchTransferInsufficientBalance() (gas: 163555)
ERC1155Test:testFailSafeBatchTransferInsufficientBalance(address,uint256[],uint256[],uint256[],bytes,bytes) (runs: 256, μ: 1808833, ~: 648144)
ERC1155Test:testFailSafeTransferFromInsufficientBalance() (gas: 63245)
ERC1155Test:testFailSafeTransferFromInsufficientBalance(address,uint256,uint256,uint256,bytes,bytes) (runs: 256, μ: 62944, ~: 67391)
ERC1155Test:testFailSafeTransferFromSelfInsufficientBalance() (gas: 34297)
ERC1155Test:testFailSafeTransferFromSelfInsufficientBalance(address,uint256,uint256,uint256,bytes,bytes) (runs: 256, μ: 35671, ~: 38477)
ERC1155Test:testFailSafeTransferFromToNonERC155Recipient() (gas: 96510)
ERC1155Test:testFailSafeTransferFromToNonERC155Recipient(uint256,uint256,uint256,bytes,bytes) (runs: 256, μ: 96657, ~: 100546)
ERC1155Test:testFailSafeTransferFromToRevertingERC1155Recipient() (gas: 287731)
ERC1155Test:testFailSafeTransferFromToRevertingERC1155Recipient(uint256,uint256,uint256,bytes,bytes) (runs: 256, μ: 287828, ~: 291719)
ERC1155Test:testFailSafeTransferFromToWrongReturnDataERC1155Recipient() (gas: 239587)
ERC1155Test:testFailSafeTransferFromToWrongReturnDataERC1155Recipient(uint256,uint256,uint256,bytes,bytes) (runs: 256, μ: 239707, ~: 243598)
ERC1155Test:testFailSafeTransferFromToZero() (gas: 62014)
ERC1155Test:testFailSafeTransferFromToZero(uint256,uint256,uint256,bytes,bytes) (runs: 256, μ: 62068, ~: 66037)
ERC1155Test:testMintToEOA() (gas: 34765)
ERC1155Test:testMintToEOA(address,uint256,uint256,bytes) (runs: 256, μ: 35273, ~: 35907)
ERC1155Test:testMintToERC1155Recipient() (gas: 661411)
ERC1155Test:testMintToERC1155Recipient(uint256,uint256,bytes) (runs: 256, μ: 691094, ~: 684374)
ERC1155Test:testSafeBatchTransferFromToEOA() (gas: 297822)
ERC1155Test:testSafeBatchTransferFromToEOA(address,uint256[],uint256[],uint256[],bytes,bytes) (runs: 256, μ: 5017684, ~: 4010502)
ERC1155Test:testSafeBatchTransferFromToERC1155Recipient() (gas: 1175327)
ERC1155Test:testSafeBatchTransferFromToERC1155Recipient(uint256[],uint256[],uint256[],bytes,bytes) (runs: 256, μ: 7759463, ~: 6618414)
ERC1155Test:testSafeTransferFromSelf() (gas: 64177)
ERC1155Test:testSafeTransferFromSelf(uint256,uint256,bytes,uint256,address,bytes) (runs: 256, μ: 64681, ~: 68564)
ERC1155Test:testSafeTransferFromToEOA() (gas: 93167)
ERC1155Test:testSafeTransferFromToEOA(uint256,uint256,bytes,uint256,address,bytes) (runs: 256, μ: 93963, ~: 97450)
ERC1155Test:testSafeTransferFromToERC1155Recipient() (gas: 739583)
ERC1155Test:testSafeTransferFromToERC1155Recipient(uint256,uint256,bytes,uint256,bytes) (runs: 256, μ: 769591, ~: 765729)
ERC20Invariants:invariantBalanceSum() (runs: 256, calls: 3840, reverts: 2349)
ERC20Test:invariantMetadata() (runs: 256, calls: 3840, reverts: 2566)
ERC20Test:testApprove() (gas: 31058)
ERC20Test:testApprove(address,uint256) (runs: 256, μ: 30036, ~: 31280)
ERC20Test:testBurn() (gas: 56970)
ERC20Test:testBurn(address,uint256,uint256) (runs: 256, μ: 57875, ~: 59645)
ERC20Test:testFailBurnInsufficientBalance(address,uint256,uint256) (runs: 256, μ: 52089, ~: 55492)
ERC20Test:testFailPermitBadDeadline() (gas: 36935)
ERC20Test:testFailPermitBadDeadline(uint256,address,uint256,uint256) (runs: 256, μ: 31831, ~: 37218)
ERC20Test:testFailPermitBadNonce() (gas: 36874)
ERC20Test:testFailPermitBadNonce(uint256,address,uint256,uint256,uint256) (runs: 256, μ: 32158, ~: 37187)
ERC20Test:testFailPermitPastDeadline() (gas: 11191)
ERC20Test:testFailPermitPastDeadline(uint256,address,uint256,uint256) (runs: 256, μ: 11916, ~: 13101)
ERC20Test:testFailPermitReplay() (gas: 66274)
ERC20Test:testFailPermitReplay(uint256,address,uint256,uint256) (runs: 256, μ: 56082, ~: 66592)
ERC20Test:testFailTransferFromInsufficientAllowance() (gas: 80882)
ERC20Test:testFailTransferFromInsufficientAllowance(address,uint256,uint256) (runs: 256, μ: 79521, ~: 83393)
ERC20Test:testFailTransferFromInsufficientBalance() (gas: 81358)
ERC20Test:testFailTransferFromInsufficientBalance(address,uint256,uint256) (runs: 256, μ: 79323, ~: 83870)
ERC20Test:testFailTransferInsufficientBalance() (gas: 52806)
ERC20Test:testFailTransferInsufficientBalance(address,uint256,uint256) (runs: 256, μ: 51912, ~: 55310)
ERC20Test:testInfiniteApproveTransferFrom() (gas: 89793)
ERC20Test:testMetadata(string,string,uint8) (runs: 256, μ: 846655, ~: 840944)
ERC20Test:testMint() (gas: 53746)
ERC20Test:testMint(address,uint256) (runs: 256, μ: 51437, ~: 53925)
ERC20Test:testPermit() (gas: 63193)
ERC20Test:testPermit(uint248,address,uint256,uint256) (runs: 256, μ: 62506, ~: 63517)
ERC20Test:testTransfer() (gas: 60272)
ERC20Test:testTransfer(address,uint256) (runs: 256, μ: 57996, ~: 60484)
ERC20Test:testTransferFrom() (gas: 83777)
ERC20Test:testTransferFrom(address,uint256,uint256) (runs: 256, μ: 86993, ~: 92841)
ERC4626Test:invariantMetadata() (runs: 256, calls: 3840, reverts: 2883)
ERC4626Test:testFailDepositWithNoApproval() (gas: 13369)
ERC4626Test:testFailDepositWithNotEnoughApproval() (gas: 87005)
ERC4626Test:testFailDepositZero() (gas: 7780)
ERC4626Test:testFailMintWithNoApproval() (gas: 13308)
ERC4626Test:testFailRedeemWithNoShareAmount() (gas: 32342)
ERC4626Test:testFailRedeemWithNotEnoughShareAmount() (gas: 203643)
ERC4626Test:testFailRedeemZero() (gas: 7967)
ERC4626Test:testFailWithdrawWithNoUnderlyingAmount() (gas: 32289)
ERC4626Test:testFailWithdrawWithNotEnoughUnderlyingAmount() (gas: 203607)
ERC4626Test:testMetadata(string,string) (runs: 256, μ: 1471865, ~: 1472396)
ERC4626Test:testMintZero() (gas: 54607)
ERC4626Test:testMultipleMintDepositRedeemWithdraw() (gas: 411804)
ERC4626Test:testSingleDepositWithdraw(uint128) (runs: 256, μ: 201539, ~: 201550)
ERC4626Test:testSingleMintRedeem(uint128) (runs: 256, μ: 201465, ~: 201476)
ERC4626Test:testVaultInteractionsForSomeoneElse() (gas: 286238)
ERC4626Test:testWithdrawZero() (gas: 52468)
ERC6909Test:testApprove() (gas: 31572)
ERC6909Test:testApprove(address,uint256,uint256) (runs: 256, μ: 30860, ~: 31793)
ERC6909Test:testBurn() (gas: 35411)
ERC6909Test:testBurn(address,uint256,uint256) (runs: 256, μ: 23773, ~: 24153)
ERC6909Test:testFailMintBalanceOverflow() (gas: 31727)
ERC6909Test:testFailTransferBalanceOverflow() (gas: 85598)
ERC6909Test:testFailTransferBalanceOverflow(address,address,uint256,uint256) (runs: 256, μ: 87909, ~: 87909)
ERC6909Test:testFailTransferBalanceUnderflow() (gas: 10847)
ERC6909Test:testFailTransferBalanceUnderflow(address,address,uint256,uint256) (runs: 256, μ: 13194, ~: 13194)
ERC6909Test:testFailTransferFromBalanceOverflow() (gas: 85554)
ERC6909Test:testFailTransferFromBalanceOverflow(address,address,uint256,uint256) (runs: 256, μ: 88327, ~: 88327)
ERC6909Test:testFailTransferFromBalanceUnderflow() (gas: 10868)
ERC6909Test:testFailTransferFromBalanceUnderflow(address,address,uint256,uint256) (runs: 256, μ: 13424, ~: 13424)
ERC6909Test:testFailTransferFromNotAuthorized() (gas: 36078)
ERC6909Test:testFailTransferFromNotAuthorized(address,address,uint256,uint256) (runs: 256, μ: 38677, ~: 38677)
ERC6909Test:testMint() (gas: 31579)
ERC6909Test:testMint(address,uint256,uint256) (runs: 256, μ: 30878, ~: 31811)
ERC6909Test:testSetOperator() (gas: 31083)
ERC6909Test:testSetOperator(address,bool) (runs: 256, μ: 22873, ~: 31411)
ERC6909Test:testTransfer() (gas: 61874)
ERC6909Test:testTransfer(address,address,uint256,uint256,uint256) (runs: 256, μ: 59908, ~: 64506)
ERC6909Test:testTransferFromAsOperator() (gas: 87418)
ERC6909Test:testTransferFromAsOperator(address,address,uint256,uint256,uint256) (runs: 256, μ: 85351, ~: 90112)
ERC6909Test:testTransferFromWithApproval() (gas: 91890)
ERC6909Test:testTransferFromWithApproval(address,address,uint256,uint256,uint256) (runs: 256, μ: 88775, ~: 94697)
ERC6909Test:testTransferFromWithInfiniteApproval() (gas: 91541)
ERC6909Test:testTransferFromWithInfiniteApproval(address,address,uint256,uint256,uint256) (runs: 256, μ: 89501, ~: 94262)
ERC721Test:invariantMetadata() (runs: 256, calls: 3840, reverts: 2192)
ERC721Test:testApprove() (gas: 78427)
ERC721Test:testApprove(address,uint256) (runs: 256, μ: 78637, ~: 78637)
ERC721Test:testApproveAll() (gas: 31063)
ERC721Test:testApproveAll(address,bool) (runs: 256, μ: 22869, ~: 31407)
ERC721Test:testApproveBurn() (gas: 65550)
ERC721Test:testApproveBurn(address,uint256) (runs: 256, μ: 65609, ~: 65621)
ERC721Test:testBurn() (gas: 46107)
ERC721Test:testBurn(address,uint256) (runs: 256, μ: 46148, ~: 46160)
ERC721Test:testFailApproveUnAuthorized() (gas: 55598)
ERC721Test:testFailApproveUnAuthorized(address,uint256,address) (runs: 256, μ: 55872, ~: 55873)
ERC721Test:testFailApproveUnMinted() (gas: 10236)
ERC721Test:testFailApproveUnMinted(uint256,address) (runs: 256, μ: 10363, ~: 10363)
ERC721Test:testFailBalanceOfZeroAddress() (gas: 5555)
ERC721Test:testFailBurnUnMinted() (gas: 7857)
ERC721Test:testFailBurnUnMinted(uint256) (runs: 256, μ: 7938, ~: 7938)
ERC721Test:testFailDoubleBurn() (gas: 58943)
ERC721Test:testFailDoubleBurn(uint256,address) (runs: 256, μ: 59174, ~: 59174)
ERC721Test:testFailDoubleMint() (gas: 53286)
ERC721Test:testFailDoubleMint(uint256,address) (runs: 256, μ: 53496, ~: 53496)
ERC721Test:testFailMintToZero() (gas: 5753)
ERC721Test:testFailMintToZero(uint256) (runs: 256, μ: 5835, ~: 5835)
ERC721Test:testFailOwnerOfUnminted() (gas: 7609)
ERC721Test:testFailOwnerOfUnminted(uint256) (runs: 256, μ: 7689, ~: 7689)
ERC721Test:testFailSafeMintToERC721RecipientWithWrongReturnData() (gas: 159076)
ERC721Test:testFailSafeMintToERC721RecipientWithWrongReturnData(uint256) (runs: 256, μ: 159125, ~: 159125)
ERC721Test:testFailSafeMintToERC721RecipientWithWrongReturnDataWithData() (gas: 159831)
ERC721Test:testFailSafeMintToERC721RecipientWithWrongReturnDataWithData(uint256,bytes) (runs: 256, μ: 160231, ~: 160182)
ERC721Test:testFailSafeMintToNonERC721Recipient() (gas: 89210)
ERC721Test:testFailSafeMintToNonERC721Recipient(uint256) (runs: 256, μ: 89279, ~: 89279)
ERC721Test:testFailSafeMintToNonERC721RecipientWithData() (gas: 89995)
ERC721Test:testFailSafeMintToNonERC721RecipientWithData(uint256,bytes) (runs: 256, μ: 90420, ~: 90368)
ERC721Test:testFailSafeMintToRevertingERC721Recipient() (gas: 204743)
ERC721Test:testFailSafeMintToRevertingERC721Recipient(uint256) (runs: 256, μ: 204815, ~: 204815)
ERC721Test:testFailSafeMintToRevertingERC721RecipientWithData() (gas: 205517)
ERC721Test:testFailSafeMintToRevertingERC721RecipientWithData(uint256,bytes) (runs: 256, μ: 205964, ~: 205915)
ERC721Test:testFailSafeTransferFromToERC721RecipientWithWrongReturnData() (gas: 187276)
ERC721Test:testFailSafeTransferFromToERC721RecipientWithWrongReturnData(uint256) (runs: 256, μ: 187360, ~: 187360)
ERC721Test:testFailSafeTransferFromToERC721RecipientWithWrongReturnDataWithData() (gas: 187728)
ERC721Test:testFailSafeTransferFromToERC721RecipientWithWrongReturnDataWithData(uint256,bytes) (runs: 256, μ: 188067, ~: 188063)
ERC721Test:testFailSafeTransferFromToNonERC721Recipient() (gas: 117413)
ERC721Test:testFailSafeTransferFromToNonERC721Recipient(uint256) (runs: 256, μ: 117495, ~: 117495)
ERC721Test:testFailSafeTransferFromToNonERC721RecipientWithData() (gas: 117872)
ERC721Test:testFailSafeTransferFromToNonERC721RecipientWithData(uint256,bytes) (runs: 256, μ: 118280, ~: 118276)
ERC721Test:testFailSafeTransferFromToRevertingERC721Recipient() (gas: 233009)
ERC721Test:testFailSafeTransferFromToRevertingERC721Recipient(uint256) (runs: 256, μ: 233050, ~: 233050)
ERC721Test:testFailSafeTransferFromToRevertingERC721RecipientWithData() (gas: 233396)
ERC721Test:testFailSafeTransferFromToRevertingERC721RecipientWithData(uint256,bytes) (runs: 256, μ: 233823, ~: 233819)
ERC721Test:testFailTransferFromNotOwner() (gas: 57872)
ERC721Test:testFailTransferFromNotOwner(address,address,uint256) (runs: 256, μ: 58144, ~: 58162)
ERC721Test:testFailTransferFromToZero() (gas: 53381)
ERC721Test:testFailTransferFromToZero(uint256) (runs: 256, μ: 53463, ~: 53463)
ERC721Test:testFailTransferFromUnOwned() (gas: 8000)
ERC721Test:testFailTransferFromUnOwned(address,address,uint256) (runs: 256, μ: 8276, ~: 8241)
ERC721Test:testFailTransferFromWrongFrom() (gas: 53361)
ERC721Test:testFailTransferFromWrongFrom(address,address,address,uint256) (runs: 256, μ: 53194, ~: 53752)
ERC721Test:testMetadata(string,string) (runs: 256, μ: 1309634, ~: 1309984)
ERC721Test:testMint() (gas: 54336)
ERC721Test:testMint(address,uint256) (runs: 256, μ: 54521, ~: 54521)
ERC721Test:testSafeMintToEOA() (gas: 56993)
ERC721Test:testSafeMintToEOA(uint256,address) (runs: 256, μ: 56754, ~: 57421)
ERC721Test:testSafeMintToERC721Recipient() (gas: 427035)
ERC721Test:testSafeMintToERC721Recipient(uint256) (runs: 256, μ: 426053, ~: 427142)
ERC721Test:testSafeMintToERC721RecipientWithData() (gas: 448149)
ERC721Test:testSafeMintToERC721RecipientWithData(uint256,bytes) (runs: 256, μ: 480015, ~: 470905)
ERC721Test:testSafeTransferFromToEOA() (gas: 95666)
ERC721Test:testSafeTransferFromToEOA(uint256,address) (runs: 256, μ: 94979, ~: 96099)
ERC721Test:testSafeTransferFromToERC721Recipient() (gas: 485549)
ERC721Test:testSafeTransferFromToERC721Recipient(uint256) (runs: 256, μ: 484593, ~: 485682)
ERC721Test:testSafeTransferFromToERC721RecipientWithData() (gas: 506317)
ERC721Test:testSafeTransferFromToERC721RecipientWithData(uint256,bytes) (runs: 256, μ: 538126, ~: 529082)
ERC721Test:testTransferFrom() (gas: 86347)
ERC721Test:testTransferFrom(uint256,address) (runs: 256, μ: 86467, ~: 86475)
ERC721Test:testTransferFromApproveAll() (gas: 92898)
ERC721Test:testTransferFromApproveAll(uint256,address) (runs: 256, μ: 93181, ~: 93182)
ERC721Test:testTransferFromSelf() (gas: 64776)
ERC721Test:testTransferFromSelf(uint256,address) (runs: 256, μ: 65060, ~: 65061)
FixedPointMathLibTest:testDifferentiallyFuzzSqrt(uint256) (runs: 256, μ: 13868, ~: 6222)
FixedPointMathLibTest:testDivWadDown() (gas: 820)
FixedPointMathLibTest:testDivWadDown(uint256,uint256) (runs: 256, μ: 716, ~: 813)
FixedPointMathLibTest:testDivWadDownEdgeCases() (gas: 439)
FixedPointMathLibTest:testDivWadUp() (gas: 943)
FixedPointMathLibTest:testDivWadUp(uint256,uint256) (runs: 256, μ: 799, ~: 952)
FixedPointMathLibTest:testDivWadUpEdgeCases() (gas: 442)
FixedPointMathLibTest:testFailDivWadDownOverflow(uint256,uint256) (runs: 256, μ: 440, ~: 419)
FixedPointMathLibTest:testFailDivWadDownZeroDenominator() (gas: 332)
FixedPointMathLibTest:testFailDivWadDownZeroDenominator(uint256) (runs: 256, μ: 387, ~: 387)
FixedPointMathLibTest:testFailDivWadUpOverflow(uint256,uint256) (runs: 256, μ: 395, ~: 374)
FixedPointMathLibTest:testFailDivWadUpZeroDenominator() (gas: 332)
FixedPointMathLibTest:testFailDivWadUpZeroDenominator(uint256) (runs: 256, μ: 386, ~: 386)
FixedPointMathLibTest:testFailMulDivDownOverflow(uint256,uint256,uint256) (runs: 256, μ: 436, ~: 414)
FixedPointMathLibTest:testFailMulDivDownZeroDenominator() (gas: 328)
FixedPointMathLibTest:testFailMulDivDownZeroDenominator(uint256,uint256) (runs: 256, μ: 385, ~: 385)
FixedPointMathLibTest:testFailMulDivUpOverflow(uint256,uint256,uint256) (runs: 256, μ: 459, ~: 437)
FixedPointMathLibTest:testFailMulDivUpZeroDenominator() (gas: 329)
FixedPointMathLibTest:testFailMulDivUpZeroDenominator(uint256,uint256) (runs: 256, μ: 428, ~: 428)
FixedPointMathLibTest:testFailMulWadDownOverflow(uint256,uint256) (runs: 256, μ: 419, ~: 387)
FixedPointMathLibTest:testFailMulWadUpOverflow(uint256,uint256) (runs: 256, μ: 396, ~: 364)
FixedPointMathLibTest:testMulDivDown() (gas: 1813)
FixedPointMathLibTest:testMulDivDown(uint256,uint256,uint256) (runs: 256, μ: 680, ~: 786)
FixedPointMathLibTest:testMulDivDownEdgeCases() (gas: 686)
FixedPointMathLibTest:testMulDivUp() (gas: 2095)
FixedPointMathLibTest:testMulDivUp(uint256,uint256,uint256) (runs: 256, μ: 810, ~: 1034)
FixedPointMathLibTest:testMulDivUpEdgeCases() (gas: 785)
FixedPointMathLibTest:testMulWadDown() (gas: 823)
FixedPointMathLibTest:testMulWadDown(uint256,uint256) (runs: 256, μ: 688, ~: 803)
FixedPointMathLibTest:testMulWadDownEdgeCases() (gas: 822)
FixedPointMathLibTest:testMulWadUp() (gas: 921)
FixedPointMathLibTest:testMulWadUp(uint256,uint256) (runs: 256, μ: 834, ~: 1053)
FixedPointMathLibTest:testMulWadUpEdgeCases() (gas: 899)
FixedPointMathLibTest:testRPow() (gas: 2164)
FixedPointMathLibTest:testSqrt() (gas: 2580)
FixedPointMathLibTest:testSqrt(uint256) (runs: 256, μ: 997, ~: 1013)
FixedPointMathLibTest:testSqrtBack(uint256) (runs: 256, μ: 14998, ~: 340)
FixedPointMathLibTest:testSqrtBackHashed(uint256) (runs: 256, μ: 59001, ~: 59500)
FixedPointMathLibTest:testSqrtBackHashedSingle() (gas: 58937)
LibStringTest:testDifferentiallyFuzzToString(uint256,bytes) (runs: 256, μ: 20460, ~: 7749)
LibStringTest:testDifferentiallyFuzzToStringInt(int256,bytes) (runs: 256, μ: 20610, ~: 8980)
LibStringTest:testToString() (gas: 10069)
LibStringTest:testToStringDirty() (gas: 8145)
LibStringTest:testToStringIntNegative() (gas: 9634)
LibStringTest:testToStringIntPositive() (gas: 10481)
LibStringTest:testToStringOverwrite() (gas: 506)
MerkleProofLibTest:testValidProofSupplied() (gas: 2153)
MerkleProofLibTest:testVerifyEmptyMerkleProofSuppliedLeafAndRootDifferent() (gas: 1458)
MerkleProofLibTest:testVerifyEmptyMerkleProofSuppliedLeafAndRootSame() (gas: 1452)
MerkleProofLibTest:testVerifyInvalidProofSupplied() (gas: 2172)
MultiRolesAuthorityTest:testCanCallPublicCapability() (gas: 34204)
MultiRolesAuthorityTest:testCanCallPublicCapability(address,address,bytes4) (runs: 256, μ: 34392, ~: 34361)
MultiRolesAuthorityTest:testCanCallWithAuthorizedRole() (gas: 80416)
MultiRolesAuthorityTest:testCanCallWithAuthorizedRole(address,uint8,address,bytes4) (runs: 256, μ: 80700, ~: 80671)
MultiRolesAuthorityTest:testCanCallWithCustomAuthority() (gas: 422439)
MultiRolesAuthorityTest:testCanCallWithCustomAuthority(address,address,bytes4) (runs: 256, μ: 422858, ~: 422858)
MultiRolesAuthorityTest:testCanCallWithCustomAuthorityOverridesPublicCapability() (gas: 247388)
MultiRolesAuthorityTest:testCanCallWithCustomAuthorityOverridesPublicCapability(address,address,bytes4) (runs: 256, μ: 247841, ~: 247841)
MultiRolesAuthorityTest:testCanCallWithCustomAuthorityOverridesUserWithRole() (gas: 256546)
MultiRolesAuthorityTest:testCanCallWithCustomAuthorityOverridesUserWithRole(address,uint8,address,bytes4) (runs: 256, μ: 256878, ~: 256852)
MultiRolesAuthorityTest:testSetPublicCapabilities() (gas: 27727)
MultiRolesAuthorityTest:testSetPublicCapabilities(bytes4) (runs: 256, μ: 27837, ~: 27835)
MultiRolesAuthorityTest:testSetRoleCapabilities() (gas: 28932)
MultiRolesAuthorityTest:testSetRoleCapabilities(uint8,bytes4) (runs: 256, μ: 29075, ~: 29072)
MultiRolesAuthorityTest:testSetRoles() (gas: 28918)
MultiRolesAuthorityTest:testSetRoles(address,uint8) (runs: 256, μ: 29027, ~: 29014)
MultiRolesAuthorityTest:testSetTargetCustomAuthority() (gas: 28102)
MultiRolesAuthorityTest:testSetTargetCustomAuthority(address,address) (runs: 256, μ: 28115, ~: 28144)
OwnedTest:testCallFunctionAsNonOwner() (gas: 11344)
OwnedTest:testCallFunctionAsNonOwner(address) (runs: 256, μ: 16271, ~: 16290)
OwnedTest:testCallFunctionAsOwner() (gas: 10435)
OwnedTest:testTransferOwnership() (gas: 13123)
OwnedTest:testTransferOwnership(address) (runs: 256, μ: 13162, ~: 13192)
ReentrancyGuardTest:invariantReentrancyStatusAlways1() (runs: 256, calls: 3840, reverts: 254)
ReentrancyGuardTest:testFailUnprotectedCall() (gas: 46147)
ReentrancyGuardTest:testNoReentrancy() (gas: 7515)
ReentrancyGuardTest:testProtectedCall() (gas: 33467)
RolesAuthorityTest:testCanCallPublicCapability() (gas: 33409)
RolesAuthorityTest:testCanCallPublicCapability(address,address,bytes4) (runs: 256, μ: 33559, ~: 33532)
RolesAuthorityTest:testCanCallWithAuthorizedRole() (gas: 79995)
RolesAuthorityTest:testCanCallWithAuthorizedRole(address,uint8,address,bytes4) (runs: 256, μ: 80265, ~: 80238)
RolesAuthorityTest:testSetPublicCapabilities() (gas: 29095)
RolesAuthorityTest:testSetPublicCapabilities(address,bytes4) (runs: 256, μ: 29211, ~: 29192)
RolesAuthorityTest:testSetRoleCapabilities() (gas: 30276)
RolesAuthorityTest:testSetRoleCapabilities(uint8,address,bytes4) (runs: 256, μ: 30507, ~: 30489)
RolesAuthorityTest:testSetRoles() (gas: 29005)
RolesAuthorityTest:testSetRoles(address,uint8) (runs: 256, μ: 29118, ~: 29106)
SSTORE2Test:testFailReadInvalidPointer() (gas: 2927)
SSTORE2Test:testFailReadInvalidPointer(address,bytes) (runs: 256, μ: 3858, ~: 3892)
SSTORE2Test:testFailReadInvalidPointerCustomBounds() (gas: 3099)
SSTORE2Test:testFailReadInvalidPointerCustomBounds(address,uint256,uint256,bytes) (runs: 256, μ: 4072, ~: 4130)
SSTORE2Test:testFailReadInvalidPointerCustomStartBound() (gas: 3004)
SSTORE2Test:testFailReadInvalidPointerCustomStartBound(address,uint256,bytes) (runs: 256, μ: 3960, ~: 3988)
SSTORE2Test:testFailWriteReadCustomBoundsOutOfRange(bytes,uint256,uint256,bytes) (runs: 256, μ: 46236, ~: 43603)
SSTORE2Test:testFailWriteReadCustomStartBoundOutOfRange(bytes,uint256,bytes) (runs: 256, μ: 46017, ~: 43452)
SSTORE2Test:testFailWriteReadEmptyOutOfBounds() (gas: 34470)
SSTORE2Test:testFailWriteReadOutOfBounds() (gas: 34426)
SSTORE2Test:testFailWriteReadOutOfStartBound() (gas: 34362)
SSTORE2Test:testWriteRead() (gas: 53497)
SSTORE2Test:testWriteRead(bytes,bytes) (runs: 256, μ: 44019, ~: 41555)
SSTORE2Test:testWriteReadCustomBounds() (gas: 34869)
SSTORE2Test:testWriteReadCustomBounds(bytes,uint256,uint256,bytes) (runs: 256, μ: 28531, ~: 41976)
SSTORE2Test:testWriteReadCustomStartBound() (gas: 34740)
SSTORE2Test:testWriteReadCustomStartBound(bytes,uint256,bytes) (runs: 256, μ: 46485, ~: 44053)
SSTORE2Test:testWriteReadEmptyBound() (gas: 34677)
SSTORE2Test:testWriteReadFullBoundedRead() (gas: 53672)
SSTORE2Test:testWriteReadFullStartBound() (gas: 34764)
SafeCastLibTest:testFailSafeCastTo104() (gas: 387)
SafeCastLibTest:testFailSafeCastTo104(uint256) (runs: 256, μ: 468, ~: 468)
SafeCastLibTest:testFailSafeCastTo112() (gas: 388)
SafeCastLibTest:testFailSafeCastTo112(uint256) (runs: 256, μ: 445, ~: 445)
SafeCastLibTest:testFailSafeCastTo120() (gas: 409)
SafeCastLibTest:testFailSafeCastTo120(uint256) (runs: 256, μ: 490, ~: 490)
SafeCastLibTest:testFailSafeCastTo128() (gas: 365)
SafeCastLibTest:testFailSafeCastTo128(uint256) (runs: 256, μ: 487, ~: 487)
SafeCastLibTest:testFailSafeCastTo136() (gas: 409)
SafeCastLibTest:testFailSafeCastTo136(uint256) (runs: 256, μ: 489, ~: 489)
SafeCastLibTest:testFailSafeCastTo144() (gas: 365)
SafeCastLibTest:testFailSafeCastTo144(uint256) (runs: 256, μ: 423, ~: 423)
SafeCastLibTest:testFailSafeCastTo152() (gas: 368)
SafeCastLibTest:testFailSafeCastTo152(uint256) (runs: 256, μ: 468, ~: 468)
SafeCastLibTest:testFailSafeCastTo16() (gas: 388)
SafeCastLibTest:testFailSafeCastTo16(uint256) (runs: 256, μ: 468, ~: 468)
SafeCastLibTest:testFailSafeCastTo160() (gas: 409)
SafeCastLibTest:testFailSafeCastTo160(uint256) (runs: 256, μ: 444, ~: 444)
SafeCastLibTest:testFailSafeCastTo168() (gas: 341)
SafeCastLibTest:testFailSafeCastTo168(uint256) (runs: 256, μ: 488, ~: 488)
SafeCastLibTest:testFailSafeCastTo176() (gas: 363)
SafeCastLibTest:testFailSafeCastTo176(uint256) (runs: 256, μ: 489, ~: 489)
SafeCastLibTest:testFailSafeCastTo184() (gas: 343)
SafeCastLibTest:testFailSafeCastTo184(uint256) (runs: 256, μ: 490, ~: 490)
SafeCastLibTest:testFailSafeCastTo192() (gas: 367)
SafeCastLibTest:testFailSafeCastTo192(uint256) (runs: 256, μ: 446, ~: 446)
SafeCastLibTest:testFailSafeCastTo200() (gas: 343)
SafeCastLibTest:testFailSafeCastTo200(uint256) (runs: 256, μ: 490, ~: 490)
SafeCastLibTest:testFailSafeCastTo208() (gas: 386)
SafeCastLibTest:testFailSafeCastTo208(uint256) (runs: 256, μ: 446, ~: 446)
SafeCastLibTest:testFailSafeCastTo216() (gas: 365)
SafeCastLibTest:testFailSafeCastTo216(uint256) (runs: 256, μ: 424, ~: 424)
SafeCastLibTest:testFailSafeCastTo224() (gas: 409)
SafeCastLibTest:testFailSafeCastTo224(uint256) (runs: 256, μ: 423, ~: 423)
SafeCastLibTest:testFailSafeCastTo232() (gas: 410)
SafeCastLibTest:testFailSafeCastTo232(uint256) (runs: 256, μ: 467, ~: 467)
SafeCastLibTest:testFailSafeCastTo24() (gas: 387)
SafeCastLibTest:testFailSafeCastTo24(uint256) (runs: 256, μ: 424, ~: 424)
SafeCastLibTest:testFailSafeCastTo240() (gas: 364)
SafeCastLibTest:testFailSafeCastTo240(uint256) (runs: 256, μ: 467, ~: 467)
SafeCastLibTest:testFailSafeCastTo248() (gas: 365)
SafeCastLibTest:testFailSafeCastTo248(uint256) (runs: 256, μ: 466, ~: 466)
SafeCastLibTest:testFailSafeCastTo32() (gas: 364)
SafeCastLibTest:testFailSafeCastTo32(uint256) (runs: 256, μ: 468, ~: 468)
SafeCastLibTest:testFailSafeCastTo40() (gas: 366)
SafeCastLibTest:testFailSafeCastTo40(uint256) (runs: 256, μ: 422, ~: 422)
SafeCastLibTest:testFailSafeCastTo48() (gas: 366)
SafeCastLibTest:testFailSafeCastTo48(uint256) (runs: 256, μ: 488, ~: 488)
SafeCastLibTest:testFailSafeCastTo56() (gas: 388)
SafeCastLibTest:testFailSafeCastTo56(uint256) (runs: 256, μ: 445, ~: 445)
SafeCastLibTest:testFailSafeCastTo64() (gas: 410)
SafeCastLibTest:testFailSafeCastTo64(uint256) (runs: 256, μ: 446, ~: 446)
SafeCastLibTest:testFailSafeCastTo72() (gas: 410)
SafeCastLibTest:testFailSafeCastTo72(uint256) (runs: 256, μ: 467, ~: 467)
SafeCastLibTest:testFailSafeCastTo8() (gas: 341)
SafeCastLibTest:testFailSafeCastTo8(uint256) (runs: 256, μ: 421, ~: 421)
SafeCastLibTest:testFailSafeCastTo80() (gas: 343)
SafeCastLibTest:testFailSafeCastTo80(uint256) (runs: 256, μ: 424, ~: 424)
SafeCastLibTest:testFailSafeCastTo88() (gas: 344)
SafeCastLibTest:testFailSafeCastTo88(uint256) (runs: 256, μ: 489, ~: 489)
SafeCastLibTest:testFailSafeCastTo96() (gas: 366)
SafeCastLibTest:testFailSafeCastTo96(uint256) (runs: 256, μ: 469, ~: 469)
SafeCastLibTest:testSafeCastTo104() (gas: 515)
SafeCastLibTest:testSafeCastTo104(uint256) (runs: 256, μ: 2779, ~: 2779)
SafeCastLibTest:testSafeCastTo112() (gas: 469)
SafeCastLibTest:testSafeCastTo112(uint256) (runs: 256, μ: 2755, ~: 2755)
SafeCastLibTest:testSafeCastTo120() (gas: 491)
SafeCastLibTest:testSafeCastTo120(uint256) (runs: 256, μ: 2735, ~: 2735)
SafeCastLibTest:testSafeCastTo128() (gas: 516)
SafeCastLibTest:testSafeCastTo128(uint256) (runs: 256, μ: 2735, ~: 2735)
SafeCastLibTest:testSafeCastTo136() (gas: 470)
SafeCastLibTest:testSafeCastTo136(uint256) (runs: 256, μ: 2757, ~: 2757)
SafeCastLibTest:testSafeCastTo144() (gas: 514)
SafeCastLibTest:testSafeCastTo144(uint256) (runs: 256, μ: 2798, ~: 2798)
SafeCastLibTest:testSafeCastTo152() (gas: 494)
SafeCastLibTest:testSafeCastTo152(uint256) (runs: 256, μ: 2734, ~: 2734)
SafeCastLibTest:testSafeCastTo16() (gas: 469)
SafeCastLibTest:testSafeCastTo16(uint256) (runs: 256, μ: 2779, ~: 2779)
SafeCastLibTest:testSafeCastTo160() (gas: 491)
SafeCastLibTest:testSafeCastTo160(uint256) (runs: 256, μ: 2775, ~: 2775)
SafeCastLibTest:testSafeCastTo168() (gas: 494)
SafeCastLibTest:testSafeCastTo168(uint256) (runs: 256, μ: 2799, ~: 2799)
SafeCastLibTest:testSafeCastTo176() (gas: 493)
SafeCastLibTest:testSafeCastTo176(uint256) (runs: 256, μ: 2734, ~: 2734)
SafeCastLibTest:testSafeCastTo184() (gas: 513)
SafeCastLibTest:testSafeCastTo184(uint256) (runs: 256, μ: 2801, ~: 2801)
SafeCastLibTest:testSafeCastTo192() (gas: 494)
SafeCastLibTest:testSafeCastTo192(uint256) (runs: 256, μ: 2734, ~: 2734)
SafeCastLibTest:testSafeCastTo200() (gas: 470)
SafeCastLibTest:testSafeCastTo200(uint256) (runs: 256, μ: 2734, ~: 2734)
SafeCastLibTest:testSafeCastTo208() (gas: 472)
SafeCastLibTest:testSafeCastTo208(uint256) (runs: 256, μ: 2756, ~: 2756)
SafeCastLibTest:testSafeCastTo216() (gas: 493)
SafeCastLibTest:testSafeCastTo216(uint256) (runs: 256, μ: 2777, ~: 2777)
SafeCastLibTest:testSafeCastTo224() (gas: 469)
SafeCastLibTest:testSafeCastTo224(uint256) (runs: 256, μ: 2733, ~: 2733)
SafeCastLibTest:testSafeCastTo232() (gas: 492)
SafeCastLibTest:testSafeCastTo232(uint256) (runs: 256, μ: 2735, ~: 2735)
SafeCastLibTest:testSafeCastTo24() (gas: 515)
SafeCastLibTest:testSafeCastTo24(uint256) (runs: 256, μ: 2733, ~: 2733)
SafeCastLibTest:testSafeCastTo240() (gas: 513)
SafeCastLibTest:testSafeCastTo240(uint256) (runs: 256, μ: 2800, ~: 2800)
SafeCastLibTest:testSafeCastTo248() (gas: 472)
SafeCastLibTest:testSafeCastTo248(uint256) (runs: 256, μ: 2777, ~: 2777)
SafeCastLibTest:testSafeCastTo32() (gas: 516)
SafeCastLibTest:testSafeCastTo32(uint256) (runs: 256, μ: 2777, ~: 2777)
SafeCastLibTest:testSafeCastTo40() (gas: 517)
SafeCastLibTest:testSafeCastTo40(uint256) (runs: 256, μ: 2756, ~: 2756)
SafeCastLibTest:testSafeCastTo48() (gas: 469)
SafeCastLibTest:testSafeCastTo48(uint256) (runs: 256, μ: 2778, ~: 2778)
SafeCastLibTest:testSafeCastTo56() (gas: 470)
SafeCastLibTest:testSafeCastTo56(uint256) (runs: 256, μ: 2801, ~: 2801)
SafeCastLibTest:testSafeCastTo64() (gas: 537)
SafeCastLibTest:testSafeCastTo64(uint256) (runs: 256, μ: 2799, ~: 2799)
SafeCastLibTest:testSafeCastTo72(uint256) (runs: 256, μ: 2798, ~: 2798)
SafeCastLibTest:testSafeCastTo8() (gas: 513)
SafeCastLibTest:testSafeCastTo8(uint256) (runs: 256, μ: 2755, ~: 2755)
SafeCastLibTest:testSafeCastTo80(uint256) (runs: 256, μ: 2736, ~: 2736)
SafeCastLibTest:testSafeCastTo88(uint256) (runs: 256, μ: 2755, ~: 2755)
SafeCastLibTest:testSafeCastTo96() (gas: 536)
SafeCastLibTest:testSafeCastTo96(uint256) (runs: 256, μ: 2800, ~: 2800)
SafeTransferLibTest:testApproveWithGarbage(address,uint256,bytes,bytes) (runs: 256, μ: 3016, ~: 2231)
SafeTransferLibTest:testApproveWithMissingReturn() (gas: 30757)
SafeTransferLibTest:testApproveWithMissingReturn(address,uint256,bytes) (runs: 256, μ: 30800, ~: 31572)
SafeTransferLibTest:testApproveWithNonContract() (gas: 3041)
SafeTransferLibTest:testApproveWithNonContract(address,address,uint256,bytes) (runs: 256, μ: 4095, ~: 4123)
SafeTransferLibTest:testApproveWithReturnsTooMuch() (gas: 31140)
SafeTransferLibTest:testApproveWithReturnsTooMuch(address,uint256,bytes) (runs: 256, μ: 31268, ~: 32040)
SafeTransferLibTest:testApproveWithStandardERC20() (gas: 30888)
SafeTransferLibTest:testApproveWithStandardERC20(address,uint256,bytes) (runs: 256, μ: 30994, ~: 31766)
SafeTransferLibTest:testFailApproveWithGarbage(address,uint256,bytes,bytes) (runs: 256, μ: 85288, ~: 77915)
SafeTransferLibTest:testFailApproveWithReturnsFalse() (gas: 5633)
SafeTransferLibTest:testFailApproveWithReturnsFalse(address,uint256,bytes) (runs: 256, μ: 6486, ~: 6481)
SafeTransferLibTest:testFailApproveWithReturnsTooLittle() (gas: 5574)
SafeTransferLibTest:testFailApproveWithReturnsTooLittle(address,uint256,bytes) (runs: 256, μ: 6450, ~: 6445)
SafeTransferLibTest:testFailApproveWithReturnsTwo(address,uint256,bytes) (runs: 256, μ: 6458, ~: 6453)
SafeTransferLibTest:testFailApproveWithReverting() (gas: 5508)
SafeTransferLibTest:testFailApproveWithReverting(address,uint256,bytes) (runs: 256, μ: 6409, ~: 6404)
SafeTransferLibTest:testFailTransferETHToContractWithoutFallback() (gas: 7244)
SafeTransferLibTest:testFailTransferETHToContractWithoutFallback(uint256,bytes) (runs: 256, μ: 7758, ~: 8055)
SafeTransferLibTest:testFailTransferFromWithGarbage(address,address,uint256,bytes,bytes) (runs: 256, μ: 120098, ~: 117413)
SafeTransferLibTest:testFailTransferFromWithReturnsFalse() (gas: 13675)
SafeTransferLibTest:testFailTransferFromWithReturnsFalse(address,address,uint256,bytes) (runs: 256, μ: 14606, ~: 14600)
SafeTransferLibTest:testFailTransferFromWithReturnsTooLittle() (gas: 13556)
SafeTransferLibTest:testFailTransferFromWithReturnsTooLittle(address,address,uint256,bytes) (runs: 256, μ: 14465, ~: 14459)
SafeTransferLibTest:testFailTransferFromWithReturnsTwo(address,address,uint256,bytes) (runs: 256, μ: 14572, ~: 14566)
SafeTransferLibTest:testFailTransferFromWithReverting() (gas: 9757)
SafeTransferLibTest:testFailTransferFromWithReverting(address,address,uint256,bytes) (runs: 256, μ: 10686, ~: 10680)
SafeTransferLibTest:testFailTransferWithGarbage(address,uint256,bytes,bytes) (runs: 256, μ: 91079, ~: 83995)
SafeTransferLibTest:testFailTransferWithReturnsFalse() (gas: 8538)
SafeTransferLibTest:testFailTransferWithReturnsFalse(address,uint256,bytes) (runs: 256, μ: 9457, ~: 9452)
SafeTransferLibTest:testFailTransferWithReturnsTooLittle() (gas: 8544)
SafeTransferLibTest:testFailTransferWithReturnsTooLittle(address,uint256,bytes) (runs: 256, μ: 9397, ~: 9392)
SafeTransferLibTest:testFailTransferWithReturnsTwo(address,uint256,bytes) (runs: 256, μ: 9384, ~: 9379)
SafeTransferLibTest:testFailTransferWithReverting() (gas: 8500)
SafeTransferLibTest:testFailTransferWithReverting(address,uint256,bytes) (runs: 256, μ: 9356, ~: 9351)
SafeTransferLibTest:testTransferETH() (gas: 34592)
SafeTransferLibTest:testTransferETH(address,uint256,bytes) (runs: 256, μ: 35787, ~: 37975)
SafeTransferLibTest:testTransferFromWithGarbage(address,address,uint256,bytes,bytes) (runs: 256, μ: 3348, ~: 2253)
SafeTransferLibTest:testTransferFromWithMissingReturn() (gas: 49196)
SafeTransferLibTest:testTransferFromWithMissingReturn(address,address,uint256,bytes) (runs: 256, μ: 48997, ~: 49580)
SafeTransferLibTest:testTransferFromWithNonContract() (gas: 3047)
SafeTransferLibTest:testTransferFromWithNonContract(address,address,address,uint256,bytes) (runs: 256, μ: 4234, ~: 4240)
SafeTransferLibTest:testTransferFromWithReturnsTooMuch() (gas: 49820)
SafeTransferLibTest:testTransferFromWithReturnsTooMuch(address,address,uint256,bytes) (runs: 256, μ: 49640, ~: 50219)
SafeTransferLibTest:testTransferFromWithStandardERC20() (gas: 47612)
SafeTransferLibTest:testTransferFromWithStandardERC20(address,address,uint256,bytes) (runs: 256, μ: 47345, ~: 48031)
SafeTransferLibTest:testTransferWithGarbage(address,uint256,bytes,bytes) (runs: 256, μ: 3006, ~: 2187)
SafeTransferLibTest:testTransferWithMissingReturn() (gas: 36672)
SafeTransferLibTest:testTransferWithMissingReturn(address,uint256,bytes) (runs: 256, μ: 36539, ~: 37552)
SafeTransferLibTest:testTransferWithNonContract() (gas: 3018)
SafeTransferLibTest:testTransferWithNonContract(address,address,uint256,bytes) (runs: 256, μ: 4159, ~: 4187)
SafeTransferLibTest:testTransferWithReturnsTooMuch() (gas: 37118)
SafeTransferLibTest:testTransferWithReturnsTooMuch(address,uint256,bytes) (runs: 256, μ: 36942, ~: 37955)
SafeTransferLibTest:testTransferWithStandardERC20() (gas: 36702)
SafeTransferLibTest:testTransferWithStandardERC20(address,uint256,bytes) (runs: 256, μ: 36592, ~: 37605)
SignedWadMathTest:testFailWadDivOverflow(int256,int256) (runs: 256, μ: 347, ~: 329)
SignedWadMathTest:testFailWadDivZeroDenominator(int256) (runs: 256, μ: 296, ~: 296)
SignedWadMathTest:testFailWadMulEdgeCase() (gas: 286)
SignedWadMathTest:testFailWadMulEdgeCase2() (gas: 309)
SignedWadMathTest:testFailWadMulOverflow(int256,int256) (runs: 256, μ: 354, ~: 319)
SignedWadMathTest:testWadDiv(uint256,uint256,bool,bool) (runs: 256, μ: 5712, ~: 5714)
SignedWadMathTest:testWadMul(uint256,uint256,bool,bool) (runs: 256, μ: 5760, ~: 5762)
WETHInvariants:invariantTotalSupplyEqualsBalance() (runs: 256, calls: 3840, reverts: 1776)
WETHTest:testDeposit() (gas: 63535)
WETHTest:testDeposit(uint256) (runs: 256, μ: 63155, ~: 65880)
WETHTest:testFallbackDeposit() (gas: 63249)
WETHTest:testFallbackDeposit(uint256) (runs: 256, μ: 62879, ~: 65604)
WETHTest:testPartialWithdraw() (gas: 73281)
WETHTest:testWithdraw() (gas: 54360)
WETHTest:testWithdraw(uint256,uint256) (runs: 256, μ: 75313, ~: 78076)