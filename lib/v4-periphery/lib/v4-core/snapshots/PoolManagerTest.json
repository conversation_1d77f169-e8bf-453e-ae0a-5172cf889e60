{"addLiquidity with empty hook": "274012", "addLiquidity with native token": "135001", "donate gas with 1 token": "106214", "donate gas with 2 tokens": "145510", "erc20 collect protocol fees": "57728", "native collect protocol fees": "59371", "poolManager bytecode size": "24009", "poolManager initcode hash (without constructor params, as uint256)": "19281018184167079101887460999643277467915809731640262058315305465805214934776", "removeLiquidity with empty hook": "130613", "removeLiquidity with native token": "112523", "simple addLiquidity": "161276", "simple addLiquidity second addition same range": "98731", "simple removeLiquidity": "85099", "simple removeLiquidity some liquidity remains": "92986", "simple swap": "123144", "simple swap with native": "108434", "swap against liquidity": "116527", "swap against liquidity with native token": "105569", "swap burn 6909 for input": "129285", "swap burn native 6909 for input": "118672", "swap mint native output as 6909": "139620", "swap mint output as 6909": "154985", "swap with hooks": "132165"}